文件名：1203358157_颜盐_20250609-205030.mp4
Resolution：720x1280 / 14.95 fps
duration：05:12:17
Size：2.57GB

这段代码帮我把预览图视频信息 分成两行显示 
文件名
分辨率 视频时长 文件大小 

如下效果

文件名：1203358157_颜盐_20250609-205030.mp4
分辨率：720x1280 / 14.95 fps  
视频时长：05:12:17  
文件大小：2.57GB

每个 / 左右隔两个空格 

QPainterPath

置文字组件的背景为透明，避免覆盖iOS风格背景

我发现了问题所在。iOS风格面板被覆盖的原因是渲染顺序和背景绘制的冲突。我们的代码流程中有以下问题：
渲染顺序问题：
我们先绘制了iOS风格的背景（带圆角、边框和高光）
然后使用LeftAlignedFontPreview渲染文本，但它在渲染过程中会先填充自己的背景
即使我们设置了文本组件的背景为"透明"，实际上LeftAlignedFontPreview的渲染方法仍在填充背景
背景覆盖：
在LeftAlignedFontPreview的paintEvent方法中，它会执行：
Apply to video_captur...
这一步会使用opencv_bg_color填充整个区域，覆盖了我们之前精心设计的iOS风格背景。
要解决这个问题，我们需要修改渲染方式。我有两种方案：
修改LeftAlignedFontPreview类，添加一个选项允许跳过背景绘制
改变渲染顺序和方式，在文本渲染后再绘制iOS风格元素
建议的修改方法是：让我们直接在面板上绘制文本，而不是使用LeftAlignedFontPreview组件。这样可以完全控制绘制过程，确保iOS风格不被覆盖。
如果您希望我实施这个修复，请告诉我，我会重新设计渲染流程。


直接操作底层图像：它不是使用 CustomFontPreview 组件，而是直接在 OpenCV 的图像上进行绘制（通过 Qt 的 QPainter）
使用透明图层：
创建了一个透明的 QImage/QPixmap (info_pixmap.fill(QColor(0, 0, 0, 0)))
在这个透明层上绘制所有内容（渐变背景、文本、装饰）
最后通过 alpha 混合将透明层合并到最终图像上
单一绘制流程：整个信息面板是在一次绘制操作中完成的，而不是分散在多个组件中

直接集成到图像：最终将渲染好的信息面板使用 alpha 混合方式直接集成到最终图像中，而不是通过 QWidget 层级显示


完全重写了文本渲染方式，从使用多个独立组件渲染改为单一QPainter直接绘制文本，避免组件间的边界线问题



我们解决分割线和纯色背景填充问题的方法主要包括以下几个关键点：
统一绘制方式：
将原来使用多个独立组件（LeftAlignedFontPreview）渲染文本的方式改为使用单一的QPainter直接在同一个画布上绘制所有文本
这避免了多个组件之间可能产生的边界线和像素级差异
背景渲染优化：
使用透明背景（info_pixmap.fill(Qt.GlobalColor.transparent)）作为基础
然后用QPainterPath创建一个微小圆角的矩形区域，填充半透明背景色
这样避免了锐利的边缘和可能产生的锯齿
颜色处理改进：
直接使用设置中的颜色对象，而不再进行RGB通道转换：
Apply to video_captur...
设置适当的透明度：base_color.setAlpha(180)，增强玻璃效果
渐变和混合模式：
添加了渐变效果和高光来创造玻璃态效果，而不是简单的纯色填充
使用加法混合模式（CompositionMode_Plus）让渐变与底色混合更加自然
装饰元素优化：
使用单独的绘制操作为每行添加装饰元素，避免可能的边界问题
为装饰元素添加了光晕和渐变效果，使其与背景更好地融合
单一绘制流程：
整个信息区域在同一个绘制上下文中完成，避免了多次绘制可能产生的边界问题
先绘制背景，再绘制文本，最后添加装饰元素，确保层次清晰
这种方法从根本上解决了分割线问题，因为它避免了多个组件和多层绘制可能产生的边界线，同时通过半透明效果和渐变过渡使整个区域看起来更加连贯和现代化，没有明显的分隔感。

不再使用LeftAlignedFontPreview组件，而是直接用QPainter绘制所有内容
使用单一QPainter直接控制整个绘制过程，避免组件重绘覆盖我们的效果



                # 为每行文本创建一个LeftAlignedFontPreview实例
                for i, text in enumerate(info_texts):
                    font_preview = LeftAlignedFontPreview(left_margin=left_margin)
                    font_preview.setText(text)
                    font_preview.setFont(qt_font)
                    font_preview.setColors(info_text_color, QColor(0, 0, 0, 0))  # 透明背景
                    font_preview.setFixedSize(img_width - left_margin, line_height)
                    
                    # 渲染文本到对应位置
                    font_preview.render(painter, QPoint(left_margin, y_positions[i]))

->

                # 直接使用QPainter绘制文本，不使用LeftAlignedFontPreview
                painter.setFont(qt_font)
                painter.setPen(info_text_color)
                
                for i, text in enumerate(info_texts):
                    # 计算文本位置
                    text_x = left_margin
                    text_y = y_positions[i] + int(line_height * 0.7)  # 垂直居中
                    
                    # 直接绘制文本
                    painter.drawText(text_x, text_y, text)




