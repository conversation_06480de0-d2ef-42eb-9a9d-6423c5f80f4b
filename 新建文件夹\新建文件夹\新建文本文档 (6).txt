            # 创建一个PIL图像用于渐变背景
            info_area_pil = Image.new('RGBA', (img_width, info_bar_height), (0, 0, 0, 0))
            
            # 使用更深的灰色渐变色调 - 增强立体感
            from_color = (50, 50, 50, 255)     # #323232 - 更深的灰色
            to_color = (75, 75, 75, 255)       # #4b4b4b - 深中灰色
            
            # 创建一个带有上下渐变的背景以增强立体感 - 电影院风格
            for x in range(img_width):
                # 计算水平渐变
                r_h = int(from_color[0] + (to_color[0] - from_color[0]) * x / img_width)
                g_h = int(from_color[1] + (to_color[1] - from_color[1]) * x / img_width)
                b_h = int(from_color[2] + (to_color[2] - from_color[2]) * x / img_width)
                
                for y in range(info_bar_height):
                    # 增强垂直渐变 - 强化电影院立体感
                    # 使用幂函数使渐变更加非线性，增强立体感
                    progress = y / info_bar_height
                    # 顶部增加明显的高光，底部增加深沉的阴影
                    if progress < 0.1:  # 顶部10%区域添加高光效果
                        # 顶部高光 - 从1.25递减到1.1
                        highlight_factor = 1.25 - (progress * 1.5)
                        r = min(255, int(r_h * highlight_factor))
                        g = min(255, int(g_h * highlight_factor))
                        b = min(255, int(b_h * highlight_factor))
                    elif progress > 0.85:  # 底部15%区域添加阴影效果
                        # 底部阴影 - 从0.8递减到0.65
                        shadow_progress = (progress - 0.85) / 0.15
                        shadow_factor = 0.8 - (shadow_progress * 0.15)
                        r = int(r_h * shadow_factor)
                        g = int(g_h * shadow_factor)
                        b = int(b_h * shadow_factor)
                    else:
                        # 中间区域 - 从1.1到0.8的平滑渐变
                        mid_progress = (progress - 0.1) / 0.75  # 重新映射到0-1
                        brightness_factor = 1.1 - (mid_progress * 0.3)
                        r = min(255, int(r_h * brightness_factor))
                        g = min(255, int(g_h * brightness_factor))
                        b = min(255, int(b_h * brightness_factor))
                    
                    info_area_pil.putpixel((x, y), (r, g, b, 255))
            
            # 增强底部阴影和顶部光泽效果，提高电影院立体感
            shadow_thickness = int(info_bar_height * 0.08)    # 增加阴影厚度
            highlight_thickness = int(info_bar_height * 0.04) # 顶部光泽厚度
            
            # 底部阴影 - 使用更强的对比度增强立体感
            for y in range(info_bar_height - shadow_thickness, info_bar_height):
                # 使用二次函数使阴影更加自然
                progress = (y - (info_bar_height - shadow_thickness)) / shadow_thickness
                # 从0.7到0.5的亮度，更深的阴影效果
                darkness = 0.7 - (progress * progress * 0.2)
                for x in range(img_width):
                    r, g, b, a = info_area_pil.getpixel((x, y))
                    r = int(r * darkness)
                    g = int(g * darkness)
                    b = int(b * darkness)
                    info_area_pil.putpixel((x, y), (r, g, b, a))
            
            # 顶部添加更明显的亮光效果
            for y in range(highlight_thickness):
                # 使用二次函数使高光更加自然
                progress = y / highlight_thickness
                # 从1.3到1.0的亮度，增强立体感的自然过渡
                lightness = 1.3 - (progress * progress * 0.3)
                for x in range(img_width):
                    r, g, b, a = info_area_pil.getpixel((x, y))
                    r = min(255, int(r * lightness))
                    g = min(255, int(g * lightness))
                    b = min(255, int(b * lightness))
                    info_area_pil.putpixel((x, y), (r, g, b, a))
            
            # 转换回OpenCV格式
            info_area = cv2.cvtColor(np.array(info_area_pil), cv2.COLOR_RGBA2BGR)
            
            # 将渐变背景放入新图像
            if info_position == 'top':
                new_img[:info_bar_height, :] = info_area
            else:
                new_img[img_height:, :] = info_area
            
            # 创建最终图像用于文本绘制
            temp_img = Image.fromarray(cv2.cvtColor(new_img, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(temp_img)
            
            # 添加电影院风格的投影阴影效果
            if info_position == 'top':
                # 创建渐变阴影效果，从信息区域底部向下逐渐减弱
                shadow_height = 12  # 阴影高度
                max_alpha = 120  # 初始透明度
                
                # 在信息区域底部添加渐变阴影，模拟电影院屏幕效果
                for i in range(shadow_height):
                    # 计算当前行的透明度，使用非线性渐变
                    progress = i / shadow_height
                    current_alpha = max_alpha * (1 - progress * progress)  # 使用平方函数使衰减更加自然
                    shadow_color = (10, 10, 10, int(current_alpha))
                    shadow_y = info_bar_height + i
                    
                    # 画一条水平线作为阴影
                    draw.line([(0, shadow_y), (img_width, shadow_y)], fill=shadow_color, width=1)