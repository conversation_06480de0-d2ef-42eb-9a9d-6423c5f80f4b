基于我查找的代码，我现在有了实现预览图阴影效果所需的全部信息。我将为每个预览图的帧添加可调节的阴影效果，包括以下参数：Opacity（透明度）、Size（大小）、Darkness（暗度）、Color（颜色）和Offset（偏移）。
下面是我的实现方案：
看来编辑失败了，可能是因为文件内容太大。让我拆分成几个部分逐步添加：
class PreviewSettingsDialog(QDialog):
    # 添加类变量保存上次的设置
    last_settings = {
        'rows': 4,
        'cols': 4,
        'h_spacing': 10,
        'v_spacing': 10,
        'top_margin': 20,
        'bottom_margin': 20,
        'left_margin': 20,
        'right_margin': 20,
        'bg_color': None,  # 将在初始化时创建为白色
        'use_bg_image': False,
        'bg_image_path': "",
        'tile_bg': True,
        'keep_ratio': True,
        'bg_opacity': 1.0,
        'use_custom_export_layout': False,
        'export_rows': 1,
        'export_cols': 4,
        'show_video_info': True,
        'info_font_size': 14,
        'info_position': 'top',
        'info_bg_color': None,  # 信息背景颜色，将在初始化时创建为深灰色
        'info_text_color': None,  # 信息文本颜色，将在初始化时创建为白色
        # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
        # 添加字体相关设置
        'font_family': '',         # 字体名称
        'font_style': '常规',      # 字体样式
        'font_style_code': 0,      # 字体样式代码
        'font_underline': False,   # 下划线
        'font_strikeout': False,   # 删除线
        # 添加阴影相关设置
        'shadow_enabled': True,    # 是否启用阴影
        'shadow_size': 10,         # 阴影大小
        'shadow_alpha': 0.6,       # 阴影透明度
        'shadow_darkness': 0,      # 阴影暗度
        'shadow_color': None,      # 阴影颜色，将在初始化时创建
        'shadow_offset_x': 0,      # 阴影X偏移
        'shadow_offset_y': 0       # 阴影Y偏移
    }

我现在将在初始化方法中添加阴影颜色的初始化：
        # 如果last_settings中的颜色为None，初始化为默认颜色
        from PyQt6.QtGui import QColor
        if PreviewSettingsDialog.last_settings['bg_color'] is None:
            PreviewSettingsDialog.last_settings['bg_color'] = QColor(235, 235, 235)  # 浅灰色背景
        if PreviewSettingsDialog.last_settings['info_bg_color'] is None:
            PreviewSettingsDialog.last_settings['info_bg_color'] = QColor(50, 50, 50)  # 深灰色背景
        if PreviewSettingsDialog.last_settings['info_text_color'] is None:
            PreviewSettingsDialog.last_settings['info_text_color'] = QColor(255, 255, 255)  # 白色文本
        
        # 保存当前颜色设置
        self.bg_color = PreviewSettingsDialog.last_settings['bg_color']
        self.info_bg_color = PreviewSettingsDialog.last_settings['info_bg_color']
        self.info_text_color = PreviewSettingsDialog.last_settings['info_text_color']

->

        # 如果last_settings中的颜色为None，初始化为默认颜色
        from PyQt6.QtGui import QColor
        if PreviewSettingsDialog.last_settings['bg_color'] is None:
            PreviewSettingsDialog.last_settings['bg_color'] = QColor(235, 235, 235)  # 浅灰色背景
        if PreviewSettingsDialog.last_settings['info_bg_color'] is None:
            PreviewSettingsDialog.last_settings['info_bg_color'] = QColor(50, 50, 50)  # 深灰色背景
        if PreviewSettingsDialog.last_settings['info_text_color'] is None:
            PreviewSettingsDialog.last_settings['info_text_color'] = QColor(255, 255, 255)  # 白色文本
        if PreviewSettingsDialog.last_settings['shadow_color'] is None:
            PreviewSettingsDialog.last_settings['shadow_color'] = QColor(36, 9, 63)  # 默认阴影颜色
        
        # 保存当前颜色设置
        self.bg_color = PreviewSettingsDialog.last_settings['bg_color']
        self.info_bg_color = PreviewSettingsDialog.last_settings['info_bg_color']
        self.info_text_color = PreviewSettingsDialog.last_settings['info_text_color']
        self.shadow_color = PreviewSettingsDialog.last_settings['shadow_color']

接下来添加阴影设置的控件UI：

        export_layout_group.setLayout(export_layout_form)
        layout.addWidget(export_layout_group)
        
        # 按钮
        buttons = QHBoxLayout()

->

        export_layout_group.setLayout(export_layout_form)
        layout.addWidget(export_layout_group)
        
        # 添加阴影设置组
        shadow_group = QGroupBox("阴影效果")
        shadow_layout = QVBoxLayout()
        
        # 启用阴影效果
        self.shadow_enabled_check = QCheckBox("启用阴影效果")
        self.shadow_enabled_check.setChecked(PreviewSettingsDialog.last_settings['shadow_enabled'])
        shadow_layout.addWidget(self.shadow_enabled_check)
        
        # 阴影参数设置
        shadow_param_layout = QFormLayout()
        
        # 阴影大小设置
        self.shadow_size_spin = QSpinBox()
        self.shadow_size_spin.setRange(1, 50)
        self.shadow_size_spin.setValue(PreviewSettingsDialog.last_settings['shadow_size'])
        shadow_param_layout.addRow("阴影大小:", self.shadow_size_spin)
        
        # 阴影透明度设置
        alpha_layout = QHBoxLayout()
        self.shadow_alpha_slider = QSlider(Qt.Orientation.Horizontal)
        self.shadow_alpha_slider.setRange(0, 100)
        self.shadow_alpha_slider.setValue(int(PreviewSettingsDialog.last_settings['shadow_alpha'] * 100))
        self.shadow_alpha_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.shadow_alpha_slider.setTickInterval(10)
        alpha_layout.addWidget(self.shadow_alpha_slider)
        
        self.shadow_alpha_label = QLabel(f"{int(PreviewSettingsDialog.last_settings['shadow_alpha'] * 100)}%")
        self.shadow_alpha_slider.valueChanged.connect(
            lambda v: self.shadow_alpha_label.setText(f"{v}%"))
        alpha_layout.addWidget(self.shadow_alpha_label)
        shadow_param_layout.addRow("透明度:", alpha_layout)
        
        # 阴影暗度设置
        darkness_layout = QHBoxLayout()
        self.shadow_darkness_spin = QSpinBox()
        self.shadow_darkness_spin.setRange(0, 100)
        self.shadow_darkness_spin.setValue(PreviewSettingsDialog.last_settings['shadow_darkness'])
        darkness_layout.addWidget(self.shadow_darkness_spin)
        shadow_param_layout.addRow("暗度:", darkness_layout)
        
        # 阴影偏移设置
        offset_layout = QHBoxLayout()
        self.shadow_offset_x_spin = QSpinBox()
        self.shadow_offset_x_spin.setRange(-50, 50)
        self.shadow_offset_x_spin.setValue(PreviewSettingsDialog.last_settings['shadow_offset_x'])
        offset_layout.addWidget(QLabel("X:"))
        offset_layout.addWidget(self.shadow_offset_x_spin)
        
        self.shadow_offset_y_spin = QSpinBox()
        self.shadow_offset_y_spin.setRange(-50, 50)
        self.shadow_offset_y_spin.setValue(PreviewSettingsDialog.last_settings['shadow_offset_y'])
        offset_layout.addWidget(QLabel("Y:"))
        offset_layout.addWidget(self.shadow_offset_y_spin)
        
        shadow_param_layout.addRow("偏移:", offset_layout)
        
        # 阴影颜色设置
        shadow_color_layout = QHBoxLayout()
        
        self.shadow_color_btn = QPushButton()
        self.shadow_color_btn.setFixedSize(30, 20)
        self.shadow_color_btn.setStyleSheet(f"background-color: {self.shadow_color.name()}; border: 1px solid #999;")
        self.shadow_color_btn.clicked.connect(self.choose_shadow_color)
        shadow_color_layout.addWidget(self.shadow_color_btn)
        
        # 添加预设阴影颜色按钮
        for color in [QColor(36, 9, 63), QColor(50, 50, 50), QColor(0, 0, 0), 
                      QColor(90, 70, 50), QColor(50, 70, 100)]:
            preset_btn = QPushButton()
            preset_btn.setFixedSize(20, 20)
            preset_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #999;")
            preset_btn.clicked.connect(lambda checked, c=color: self.set_shadow_color(c))
            shadow_color_layout.addWidget(preset_btn)
        
        shadow_color_layout.addStretch()
        shadow_param_layout.addRow("颜色:", shadow_color_layout)
        
        shadow_layout.addLayout(shadow_param_layout)
        
        shadow_group.setLayout(shadow_layout)
        layout.addWidget(shadow_group)
        
        # 按钮
        buttons = QHBoxLayout()



添加阴影颜色选择方法：
The string to replace was not found in the file.
让我先看一下文件内容，找到正确的位置：
现在我可以在找到的位置添加阴影相关方法：

    def set_info_text_color(self, color):
        """设置预设信息文本颜色"""
        # 直接使用颜色，不再交换通道
        self.info_text_color = color
        self.update_info_text_color_button()
        self.on_setting_changed()

->

    def set_info_text_color(self, color):
        """设置预设信息文本颜色"""
        # 直接使用颜色，不再交换通道
        self.info_text_color = color
        self.update_info_text_color_button()
        self.on_setting_changed()
        
    # 添加阴影颜色相关方法
    def choose_shadow_color(self):
        """选择阴影颜色"""
        from PyQt6.QtWidgets import QColorDialog
        color = QColorDialog.getColor(self.shadow_color, self, "选择阴影颜色")
        if color.isValid():
            self.set_shadow_color(color)

    def set_shadow_color(self, color):
        """设置阴影颜色"""
        self.shadow_color = color
        PreviewSettingsDialog.last_settings['shadow_color'] = color
        self.shadow_color_btn.setStyleSheet(f"background-color: {color.name()}; border: 1px solid #999;")
        self.on_setting_changed()


接下来更新信号连接方法以支持阴影设置：


    def connect_preview_signals(self):
        """连接预览更新信号"""
        # 连接所有可能影响预览的控件信号
        self.rows_spin.valueChanged.connect(self.on_setting_changed)
        self.cols_spin.valueChanged.connect(self.on_setting_changed)
        self.h_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.v_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.top_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.bottom_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.left_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.right_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.opacity_slider.valueChanged.connect(self.on_setting_changed)
        self.tile_bg_check.stateChanged.connect(self.on_setting_changed)
        self.keep_ratio_check.stateChanged.connect(self.on_setting_changed)
        self.export_rows_spin.valueChanged.connect(self.on_setting_changed)
        self.export_cols_spin.valueChanged.connect(self.on_setting_changed)
        
        # 添加新的信号连接
        self.show_info_check.stateChanged.connect(self.on_setting_changed)
        self.font_size_combo.currentTextChanged.connect(self.on_setting_changed)
        self.info_pos_combo.currentTextChanged.connect(self.on_setting_changed)
        
        # 连接字体设置相关信号
        self.font_combo.currentFontChanged.connect(self.on_setting_changed)
        self.style_combo.currentTextChanged.connect(self.on_setting_changed)
        self.underline_check.stateChanged.connect(self.on_setting_changed)
        self.strikeout_check.stateChanged.connect(self.on_setting_changed)
        
        # 字体偏移控制信号已移除，现在使用Qt的自动居中文本

->

    def connect_preview_signals(self):
        """连接预览更新信号"""
        # 连接所有可能影响预览的控件信号
        self.rows_spin.valueChanged.connect(self.on_setting_changed)
        self.cols_spin.valueChanged.connect(self.on_setting_changed)
        self.h_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.v_spacing_spin.valueChanged.connect(self.on_setting_changed)
        self.top_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.bottom_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.left_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.right_margin_spin.valueChanged.connect(self.on_setting_changed)
        self.opacity_slider.valueChanged.connect(self.on_setting_changed)
        self.tile_bg_check.stateChanged.connect(self.on_setting_changed)
        self.keep_ratio_check.stateChanged.connect(self.on_setting_changed)
        self.export_rows_spin.valueChanged.connect(self.on_setting_changed)
        self.export_cols_spin.valueChanged.connect(self.on_setting_changed)
        
        # 添加新的信号连接
        self.show_info_check.stateChanged.connect(self.on_setting_changed)
        self.font_size_combo.currentTextChanged.connect(self.on_setting_changed)
        self.info_pos_combo.currentTextChanged.connect(self.on_setting_changed)
        
        # 连接字体设置相关信号
        self.font_combo.currentFontChanged.connect(self.on_setting_changed)
        self.style_combo.currentTextChanged.connect(self.on_setting_changed)
        self.underline_check.stateChanged.connect(self.on_setting_changed)
        self.strikeout_check.stateChanged.connect(self.on_setting_changed)
        
        # 连接阴影设置相关信号
        self.shadow_enabled_check.stateChanged.connect(self.on_setting_changed)
        self.shadow_size_spin.valueChanged.connect(self.on_setting_changed)
        self.shadow_alpha_slider.valueChanged.connect(self.on_setting_changed)
        self.shadow_darkness_spin.valueChanged.connect(self.on_setting_changed)
        self.shadow_offset_x_spin.valueChanged.connect(self.on_setting_changed)
        self.shadow_offset_y_spin.valueChanged.connect(self.on_setting_changed)
        
        # 字体偏移控制信号已移除，现在使用Qt的自动居中文本


同样更新断开信号的方法：

    def disconnect_preview_signals(self):
        """断开预览更新信号"""
        try:
            self.rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.h_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.v_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.top_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.bottom_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.left_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.right_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.opacity_slider.valueChanged.disconnect(self.on_setting_changed)
            self.tile_bg_check.stateChanged.disconnect(self.on_setting_changed)
            self.keep_ratio_check.stateChanged.disconnect(self.on_setting_changed)
            self.export_rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.export_cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.show_info_check.stateChanged.disconnect(self.on_setting_changed)
            self.font_size_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.info_pos_combo.currentTextChanged.disconnect(self.on_setting_changed)
            
            # 断开字体设置相关信号
            self.font_combo.currentFontChanged.disconnect(self.on_setting_changed)
            self.style_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.underline_check.stateChanged.disconnect(self.on_setting_changed)
            self.strikeout_check.stateChanged.disconnect(self.on_setting_changed)
            
            # 字体偏移控制信号已移除，现在使用Qt的自动居中文本
        except:
            # 忽略断开信号时的错误
            pass

->

    def disconnect_preview_signals(self):
        """断开预览更新信号"""
        try:
            self.rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.h_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.v_spacing_spin.valueChanged.disconnect(self.on_setting_changed)
            self.top_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.bottom_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.left_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.right_margin_spin.valueChanged.disconnect(self.on_setting_changed)
            self.opacity_slider.valueChanged.disconnect(self.on_setting_changed)
            self.tile_bg_check.stateChanged.disconnect(self.on_setting_changed)
            self.keep_ratio_check.stateChanged.disconnect(self.on_setting_changed)
            self.export_rows_spin.valueChanged.disconnect(self.on_setting_changed)
            self.export_cols_spin.valueChanged.disconnect(self.on_setting_changed)
            self.show_info_check.stateChanged.disconnect(self.on_setting_changed)
            self.font_size_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.info_pos_combo.currentTextChanged.disconnect(self.on_setting_changed)
            
            # 断开字体设置相关信号
            self.font_combo.currentFontChanged.disconnect(self.on_setting_changed)
            self.style_combo.currentTextChanged.disconnect(self.on_setting_changed)
            self.underline_check.stateChanged.disconnect(self.on_setting_changed)
            self.strikeout_check.stateChanged.disconnect(self.on_setting_changed)

            # 断开阴影设置相关信号
            self.shadow_enabled_check.stateChanged.disconnect(self.on_setting_changed)
            self.shadow_size_spin.valueChanged.disconnect(self.on_setting_changed)
            self.shadow_alpha_slider.valueChanged.disconnect(self.on_setting_changed)
            self.shadow_darkness_spin.valueChanged.disconnect(self.on_setting_changed)
            self.shadow_offset_x_spin.valueChanged.disconnect(self.on_setting_changed)
            self.shadow_offset_y_spin.valueChanged.disconnect(self.on_setting_changed)
            
            # 字体偏移控制信号已移除，现在使用Qt的自动居中文本
        except:
            # 忽略断开信号时的错误
            pass


最后更新get_settings方法以包含阴影相关设置：

    def get_settings(self):
        """获取当前设置"""
        settings = {
            'rows': self.rows_spin.value(),
            'cols': self.cols_spin.value(),
            'h_spacing': self.h_spacing_spin.value(),
            'v_spacing': self.v_spacing_spin.value(),
            'top_margin': self.top_margin_spin.value(),
            'bottom_margin': self.bottom_margin_spin.value(),
            'left_margin': self.left_margin_spin.value(),
            'right_margin': self.right_margin_spin.value(),
            'bg_color': self.bg_color,
            'use_bg_image': self.use_bg_img_check.isChecked(),
            'bg_image_path': self.background_image_path,
            'tile_bg': self.tile_bg_check.isChecked(),
            'keep_ratio': self.keep_ratio_check.isChecked(),
            'bg_opacity': self.opacity_slider.value() / 100.0,
            'use_custom_export_layout': self.use_custom_export_layout_check.isChecked(),
            'export_rows': self.export_rows_spin.value(),
            'export_cols': self.export_cols_spin.value(),
            # 添加视频信息设置
            'show_video_info': self.show_info_check.isChecked(),
            'info_font_size': self.font_size_combo.currentText(),  # 保存中文字号名称
            'info_position': 'top' if self.info_pos_combo.currentText() == "顶部" else 'bottom',
            # 添加视频信息颜色设置
            'info_bg_color': self.info_bg_color,
            'info_text_color': self.info_text_color,
            # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
            # 添加字体相关设置
            'font_family': self.font_combo.currentFont().family(),
            'font_style': self.style_combo.currentText(),
            'font_style_code': self.style_combo.currentIndex(),
            'font_underline': self.underline_check.isChecked(),
            'font_strikeout': self.strikeout_check.isChecked(),

->

    def get_settings(self):
        """获取当前设置"""
        settings = {
            'rows': self.rows_spin.value(),
            'cols': self.cols_spin.value(),
            'h_spacing': self.h_spacing_spin.value(),
            'v_spacing': self.v_spacing_spin.value(),
            'top_margin': self.top_margin_spin.value(),
            'bottom_margin': self.bottom_margin_spin.value(),
            'left_margin': self.left_margin_spin.value(),
            'right_margin': self.right_margin_spin.value(),
            'bg_color': self.bg_color,
            'use_bg_image': self.use_bg_img_check.isChecked(),
            'bg_image_path': self.background_image_path,
            'tile_bg': self.tile_bg_check.isChecked(),
            'keep_ratio': self.keep_ratio_check.isChecked(),
            'bg_opacity': self.opacity_slider.value() / 100.0,
            'use_custom_export_layout': self.use_custom_export_layout_check.isChecked(),
            'export_rows': self.export_rows_spin.value(),
            'export_cols': self.export_cols_spin.value(),
            # 添加视频信息设置
            'show_video_info': self.show_info_check.isChecked(),
            'info_font_size': self.font_size_combo.currentText(),  # 保存中文字号名称
            'info_position': 'top' if self.info_pos_combo.currentText() == "顶部" else 'bottom',
            # 添加视频信息颜色设置
            'info_bg_color': self.info_bg_color,
            'info_text_color': self.info_text_color,
            # 字体垂直偏移设置已移除，现在使用Qt的自动居中文本
            # 添加字体相关设置
            'font_family': self.font_combo.currentFont().family(),
            'font_style': self.style_combo.currentText(),
            'font_style_code': self.style_combo.currentIndex(),
            'font_underline': self.underline_check.isChecked(),
            'font_strikeout': self.strikeout_check.isChecked(),
            # 添加阴影效果设置
            'shadow_enabled': self.shadow_enabled_check.isChecked(),
            'shadow_size': self.shadow_size_spin.value(),
            'shadow_alpha': self.shadow_alpha_slider.value() / 100.0,
            'shadow_darkness': self.shadow_darkness_spin.value(), 
            'shadow_color': self.shadow_color,
            'shadow_offset_x': self.shadow_offset_x_spin.value(),
            'shadow_offset_y': self.shadow_offset_y_spin.value(),


似乎有个重复添加的内容，让我修复一下：


            # 添加阴影效果设置
            'shadow_enabled': self.shadow_enabled_check.isChecked(),
            'shadow_size': self.shadow_size_spin.value(),
            'shadow_alpha': self.shadow_alpha_slider.value() / 100.0,
            'shadow_darkness': self.shadow_darkness_spin.value(), 
            'shadow_color': self.shadow_color,
            'shadow_offset_x': self.shadow_offset_x_spin.value(),
            'shadow_offset_y': self.shadow_offset_y_spin.value(),
            # 添加阴影相关设置
            'shadow_enabled': True,    # 是否启用阴影
            'shadow_size': 10,         # 阴影大小
            'shadow_alpha': 0.6,       # 阴影透明度
            'shadow_darkness': 0,      # 阴影暗度
            'shadow_color': None,      # 阴影颜色，将在初始化时创建
            'shadow_offset_x': 0,      # 阴影X偏移
            'shadow_offset_y': 0       # 阴影Y偏移

->

            # 添加阴影效果设置
            'shadow_enabled': self.shadow_enabled_check.isChecked(),
            'shadow_size': self.shadow_size_spin.value(),
            'shadow_alpha': self.shadow_alpha_slider.value() / 100.0,
            'shadow_darkness': self.shadow_darkness_spin.value(), 
            'shadow_color': self.shadow_color,
            'shadow_offset_x': self.shadow_offset_x_spin.value(),
            'shadow_offset_y': self.shadow_offset_y_spin.value(),



现在我们需要更新预览图中每个帧的渲染代码，为其添加阴影效果。让我找到相关代码：
现在我需要找到主文件中的generate_preview方法中适当的位置添加阴影效果代码：
我需要修改在生成预览图时添加阴影效果。根据查看的代码，我需要在将帧放入预览图的部分添加阴影处理：


                    # 调整帧大小
                    resized_frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                    
                    # 将帧放入预览图
                    preview_img[y:y+cell_height, x:x+cell_width] = resized_frame

->

                    # 调整帧大小
                    resized_frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                    
                    # 从设置中获取阴影效果参数
                    shadow_enabled = settings.get('shadow_enabled', True)
                    if shadow_enabled:
                        shadow_size = settings.get('shadow_size', 10)
                        shadow_alpha = settings.get('shadow_alpha', 0.6)
                        shadow_darkness = settings.get('shadow_darkness', 0)
                        shadow_offset_x = settings.get('shadow_offset_x', 0)
                        shadow_offset_y = settings.get('shadow_offset_y', 0)
                        shadow_color_qt = settings.get('shadow_color')
                        
                        # 确保shadow_color_qt不为None
                        if shadow_color_qt is None:
                            shadow_color = np.array([36, 9, 63], dtype=np.uint8)  # 默认BGR颜色
                        else:
                            shadow_color = np.array([shadow_color_qt.blue(), shadow_color_qt.green(), shadow_color_qt.red()], dtype=np.uint8)  # BGR格式
                        
                        # 调整阴影暗度
                        if shadow_darkness > 0:
                            darkness_factor = shadow_darkness / 100
                            shadow_color = np.clip(shadow_color * (1 - darkness_factor), 0, 255).astype(np.uint8)
                        
                        # 创建阴影效果
                        # 首先创建一个与原帧大小相同的遮罩，考虑偏移和大小
                        total_size = shadow_size * 2
                        mask = np.zeros((cell_height + total_size, cell_width + total_size, 3), dtype=np.uint8)
                        
                        # 在遮罩中心绘制白色矩形（代表帧区域）
                        mask_center_y = shadow_size
                        mask_center_x = shadow_size
                        cv2.rectangle(mask, 
                                     (mask_center_x, mask_center_y), 
                                     (mask_center_x + cell_width - 1, mask_center_y + cell_height - 1), 
                                     (255, 255, 255), -1)
                        
                        # 对遮罩进行高斯模糊，创建阴影效果
                        shadow_mask = cv2.GaussianBlur(mask, (shadow_size*2+1, shadow_size*2+1), shadow_size/2)
                        
                        # 计算实际阴影位置，考虑偏移
                        shadow_x = x - shadow_size + shadow_offset_x
                        shadow_y = y - shadow_size + shadow_offset_y
                        
                        # 确保阴影不会超出图像边界
                        x_start = max(0, shadow_x)
                        y_start = max(0, shadow_y)
                        x_end = min(preview_img.shape[1], shadow_x + cell_width + total_size)
                        y_end = min(preview_img.shape[0], shadow_y + cell_height + total_size)
                        
                        # 计算阴影在遮罩中的对应位置
                        mask_x_start = 0 if shadow_x >= 0 else -shadow_x
                        mask_y_start = 0 if shadow_y >= 0 else -shadow_y
                        mask_width = x_end - x_start
                        mask_height = y_end - y_start
                        
                        # 提取要应用阴影的区域
                        roi = preview_img[y_start:y_end, x_start:x_end].copy()
                        
                        # 提取对应的阴影遮罩部分
                        shadow_roi = shadow_mask[mask_y_start:mask_y_start+mask_height, mask_x_start:mask_x_start+mask_width]
                        
                        # 创建阴影颜色层
                        shadow_color_layer = np.full_like(roi, shadow_color)
                        
                        # 应用阴影与背景混合
                        for c in range(3):  # 对BGR三个通道分别处理
                            alpha_channel = shadow_roi[:, :, c] / 255.0 * shadow_alpha  # 阴影透明度通道
                            roi[:, :, c] = roi[:, :, c] * (1 - alpha_channel) + shadow_color_layer[:, :, c] * alpha_channel
                        
                        # 将混合后的图像放回预览图
                        preview_img[y_start:y_end, x_start:x_end] = roi
                    
                    # 将帧放入预览图
                    preview_img[y:y+cell_height, x:x+cell_width] = resized_frame



现在我们也需要修改group_preview相关的代码，让它也支持阴影效果：
我们需要检查主文件中的create_group_preview方法，找到在其中添加阴影效果的位置：
现在我需要修改create_group_preview方法中的代码，为预览图中的帧添加阴影效果。这个需要在将帧放入预览图的部分添加：


                                # 调整大小
                                if frame.shape[0] != cell_height or frame.shape[1] != cell_width:
                                    frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                                
                                # 将区域放入预览图
                                preview_img[y:y+cell_height, x:x+cell_width] = frame

->

                                # 调整大小
                                if frame.shape[0] != cell_height or frame.shape[1] != cell_width:
                                    frame = cv2.resize(frame, (cell_width, cell_height), interpolation=cv2.INTER_AREA)
                                
                                # 从设置中获取阴影效果参数
                                shadow_enabled = settings.get('shadow_enabled', True)
                                if shadow_enabled:
                                    shadow_size = settings.get('shadow_size', 10)
                                    shadow_alpha = settings.get('shadow_alpha', 0.6)
                                    shadow_darkness = settings.get('shadow_darkness', 0)
                                    shadow_offset_x = settings.get('shadow_offset_x', 0)
                                    shadow_offset_y = settings.get('shadow_offset_y', 0)
                                    shadow_color_qt = settings.get('shadow_color')
                                    
                                    # 确保shadow_color_qt不为None
                                    if shadow_color_qt is None:
                                        shadow_color = np.array([36, 9, 63], dtype=np.uint8)  # 默认BGR颜色
                                    else:
                                        shadow_color = np.array([shadow_color_qt.blue(), shadow_color_qt.green(), shadow_color_qt.red()], dtype=np.uint8)  # BGR格式
                                    
                                    # 调整阴影暗度
                                    if shadow_darkness > 0:
                                        darkness_factor = shadow_darkness / 100
                                        shadow_color = np.clip(shadow_color * (1 - darkness_factor), 0, 255).astype(np.uint8)
                                    
                                    # 创建阴影效果
                                    # 首先创建一个与原帧大小相同的遮罩，考虑偏移和大小
                                    total_size = shadow_size * 2
                                    mask = np.zeros((cell_height + total_size, cell_width + total_size, 3), dtype=np.uint8)
                                    
                                    # 在遮罩中心绘制白色矩形（代表帧区域）
                                    mask_center_y = shadow_size
                                    mask_center_x = shadow_size
                                    cv2.rectangle(mask, 
                                                 (mask_center_x, mask_center_y), 
                                                 (mask_center_x + cell_width - 1, mask_center_y + cell_height - 1), 
                                                 (255, 255, 255), -1)
                                    
                                    # 对遮罩进行高斯模糊，创建阴影效果
                                    shadow_mask = cv2.GaussianBlur(mask, (shadow_size*2+1, shadow_size*2+1), shadow_size/2)
                                    
                                    # 计算实际阴影位置，考虑偏移
                                    shadow_x = x - shadow_size + shadow_offset_x
                                    shadow_y = y - shadow_size + shadow_offset_y
                                    
                                    # 确保阴影不会超出图像边界
                                    x_start = max(0, shadow_x)
                                    y_start = max(0, shadow_y)
                                    x_end = min(preview_img.shape[1], shadow_x + cell_width + total_size)
                                    y_end = min(preview_img.shape[0], shadow_y + cell_height + total_size)
                                    
                                    # 计算阴影在遮罩中的对应位置
                                    mask_x_start = 0 if shadow_x >= 0 else -shadow_x
                                    mask_y_start = 0 if shadow_y >= 0 else -shadow_y
                                    mask_width = x_end - x_start
                                    mask_height = y_end - y_start
                                    
                                    # 提取要应用阴影的区域
                                    roi = preview_img[y_start:y_end, x_start:x_end].copy()
                                    
                                    # 提取对应的阴影遮罩部分
                                    shadow_roi = shadow_mask[mask_y_start:mask_y_start+mask_height, mask_x_start:mask_x_start+mask_width]
                                    
                                    # 创建阴影颜色层
                                    shadow_color_layer = np.full_like(roi, shadow_color)
                                    
                                    # 应用阴影与背景混合
                                    for c in range(3):  # 对BGR三个通道分别处理
                                        alpha_channel = shadow_roi[:, :, c] / 255.0 * shadow_alpha  # 阴影透明度通道
                                        roi[:, :, c] = roi[:, :, c] * (1 - alpha_channel) + shadow_color_layer[:, :, c] * alpha_channel
                                    
                                    # 将混合后的图像放回预览图
                                    preview_img[y_start:y_end, x_start:x_end] = roi
                                
                                # 将区域放入预览图
                                preview_img[y:y+cell_height, x:x+cell_width] = frame




现在让我们运行一下程序，验证阴影效果是否正常工作：
我发现问题了。在视频信息区域的文本气泡部分，我们之前修改了代码，将圆角气泡替换为了直角矩形。现在需要恢复原来的圆角气泡效果。让我修复这个问题：

                # 绘制矩形
                renderer.drawRect(bubble_rect)

->

                # 绘制圆角背景
                bubble_path = QPainterPath()
                bubble_path.addRoundedRect(bubble_rect, 8, 8)  # iOS风格圆角
                
                # 设置半透明背景色 - 从文本颜色派生但更淡更透明
                bubble_color = QColor(text_colors[i])
                bubble_color.setAlphaF(0.12)  # 非常透明
                
                renderer.fillPath(bubble_path, QBrush(bubble_color))





