这段代码 设置压缩对话框的上方视频名信息显示地方  不能影响下方的任何信息显示 自动往上增高高度显示就行   无论视频名栏目显示多少个都不要影响下方

还是会影响下方显示 请再次修改代码处理好这个问题 无论视频名栏目显示多少个都不要影响下方

这段代码 设置压缩对话框 的视频名信息显示地方 为什么当超过2个显示的话 会影响下方的信息无法显示显示 我需要多少个视频名在这.里显示 都不能影响下方的任何信息显示 自动往上增高高度显示就行   无论视频名栏目显示多少个都不要影响下方

目前是这样  

ffmpeg -ss 00:05:37 -i "H:\1010\666666666\12578668_熙酱_20250610-211148.mp4" ^
-vf "eq=contrast=1.1:brightness=0.02:saturation=1.1, unsharp=5:5:0.8:3:3:0.4" ^
-vframes 1 -q:v 2 "H:\1010\666666666\screenshot_filtered.jpg"

如果直播流是rtmp://lswo308i011kachp.0csp7bqtxeaj.sourcelandchina.com/live/1871225565?token=7a97fc1814bab1ba38759e34ae154f0d&t=1749276958 这样的


vf "eq=contrast=1.1:brightness=0.02:saturation=1.1, unsharp=5:5:0.8:3:3:0.4" ^
-vframes 1 -q:v 2


提示的文件名是1871225565  这个没问题  



提示直播直播流是这种的  rtmp://pullhw002.rjtawnf.com/live/s1855878395_3cb35c1881?txSecret=8edb4c71befcd9b1429555b244012e1b&txTime=684289a2 


如果遇到这种直播流 现在提取的文件名是 s1855878395_3cb35c1881?

修改成只提取 s1855878395 
